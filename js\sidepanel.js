(() => {
  var e = {
      150: function (e, n) {
        var t, o, r;
        ("undefined" != typeof globalThis
          ? globalThis
          : "undefined" != typeof self && self,
          (o = [e]),
          (t = function (e) {
            "use strict";
            if (
              !(
                globalThis.chrome &&
                globalThis.chrome.runtime &&
                globalThis.chrome.runtime.id
              )
            )
              throw new Error(
                "This script should only be loaded in a browser extension.",
              );
            if (
              globalThis.browser &&
              globalThis.browser.runtime &&
              globalThis.browser.runtime.id
            )
              e.exports = globalThis.browser;
            else {
              const n =
                  "The message port closed before a response was received.",
                t = (e) => {
                  const t = {
                    alarms: {
                      clear: { minArgs: 0, maxArgs: 1 },
                      clearAll: { minArgs: 0, maxArgs: 0 },
                      get: { minArgs: 0, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                    },
                    bookmarks: {
                      create: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getChildren: { minArgs: 1, maxArgs: 1 },
                      getRecent: { minArgs: 1, maxArgs: 1 },
                      getSubTree: { minArgs: 1, maxArgs: 1 },
                      getTree: { minArgs: 0, maxArgs: 0 },
                      move: { minArgs: 2, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeTree: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    browserAction: {
                      disable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      enable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      getBadgeBackgroundColor: { minArgs: 1, maxArgs: 1 },
                      getBadgeText: { minArgs: 1, maxArgs: 1 },
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      openPopup: { minArgs: 0, maxArgs: 0 },
                      setBadgeBackgroundColor: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setBadgeText: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    browsingData: {
                      remove: { minArgs: 2, maxArgs: 2 },
                      removeCache: { minArgs: 1, maxArgs: 1 },
                      removeCookies: { minArgs: 1, maxArgs: 1 },
                      removeDownloads: { minArgs: 1, maxArgs: 1 },
                      removeFormData: { minArgs: 1, maxArgs: 1 },
                      removeHistory: { minArgs: 1, maxArgs: 1 },
                      removeLocalStorage: { minArgs: 1, maxArgs: 1 },
                      removePasswords: { minArgs: 1, maxArgs: 1 },
                      removePluginData: { minArgs: 1, maxArgs: 1 },
                      settings: { minArgs: 0, maxArgs: 0 },
                    },
                    commands: { getAll: { minArgs: 0, maxArgs: 0 } },
                    contextMenus: {
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeAll: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    cookies: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 1, maxArgs: 1 },
                      getAllCookieStores: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    },
                    devtools: {
                      inspectedWindow: {
                        eval: { minArgs: 1, maxArgs: 2, singleCallbackArg: !1 },
                      },
                      panels: {
                        create: {
                          minArgs: 3,
                          maxArgs: 3,
                          singleCallbackArg: !0,
                        },
                        elements: {
                          createSidebarPane: { minArgs: 1, maxArgs: 1 },
                        },
                      },
                    },
                    downloads: {
                      cancel: { minArgs: 1, maxArgs: 1 },
                      download: { minArgs: 1, maxArgs: 1 },
                      erase: { minArgs: 1, maxArgs: 1 },
                      getFileIcon: { minArgs: 1, maxArgs: 2 },
                      open: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      pause: { minArgs: 1, maxArgs: 1 },
                      removeFile: { minArgs: 1, maxArgs: 1 },
                      resume: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    extension: {
                      isAllowedFileSchemeAccess: { minArgs: 0, maxArgs: 0 },
                      isAllowedIncognitoAccess: { minArgs: 0, maxArgs: 0 },
                    },
                    history: {
                      addUrl: { minArgs: 1, maxArgs: 1 },
                      deleteAll: { minArgs: 0, maxArgs: 0 },
                      deleteRange: { minArgs: 1, maxArgs: 1 },
                      deleteUrl: { minArgs: 1, maxArgs: 1 },
                      getVisits: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                    },
                    i18n: {
                      detectLanguage: { minArgs: 1, maxArgs: 1 },
                      getAcceptLanguages: { minArgs: 0, maxArgs: 0 },
                    },
                    identity: { launchWebAuthFlow: { minArgs: 1, maxArgs: 1 } },
                    idle: { queryState: { minArgs: 1, maxArgs: 1 } },
                    management: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getSelf: { minArgs: 0, maxArgs: 0 },
                      setEnabled: { minArgs: 2, maxArgs: 2 },
                      uninstallSelf: { minArgs: 0, maxArgs: 1 },
                    },
                    notifications: {
                      clear: { minArgs: 1, maxArgs: 1 },
                      create: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getPermissionLevel: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    pageAction: {
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      hide: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    permissions: {
                      contains: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      request: { minArgs: 1, maxArgs: 1 },
                    },
                    runtime: {
                      getBackgroundPage: { minArgs: 0, maxArgs: 0 },
                      getPlatformInfo: { minArgs: 0, maxArgs: 0 },
                      openOptionsPage: { minArgs: 0, maxArgs: 0 },
                      requestUpdateCheck: { minArgs: 0, maxArgs: 0 },
                      sendMessage: { minArgs: 1, maxArgs: 3 },
                      sendNativeMessage: { minArgs: 2, maxArgs: 2 },
                      setUninstallURL: { minArgs: 1, maxArgs: 1 },
                    },
                    sessions: {
                      getDevices: { minArgs: 0, maxArgs: 1 },
                      getRecentlyClosed: { minArgs: 0, maxArgs: 1 },
                      restore: { minArgs: 0, maxArgs: 1 },
                    },
                    storage: {
                      local: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                      managed: {
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                      },
                      sync: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                    },
                    tabs: {
                      captureVisibleTab: { minArgs: 0, maxArgs: 2 },
                      create: { minArgs: 1, maxArgs: 1 },
                      detectLanguage: { minArgs: 0, maxArgs: 1 },
                      discard: { minArgs: 0, maxArgs: 1 },
                      duplicate: { minArgs: 1, maxArgs: 1 },
                      executeScript: { minArgs: 1, maxArgs: 2 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 0 },
                      getZoom: { minArgs: 0, maxArgs: 1 },
                      getZoomSettings: { minArgs: 0, maxArgs: 1 },
                      goBack: { minArgs: 0, maxArgs: 1 },
                      goForward: { minArgs: 0, maxArgs: 1 },
                      highlight: { minArgs: 1, maxArgs: 1 },
                      insertCSS: { minArgs: 1, maxArgs: 2 },
                      move: { minArgs: 2, maxArgs: 2 },
                      query: { minArgs: 1, maxArgs: 1 },
                      reload: { minArgs: 0, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeCSS: { minArgs: 1, maxArgs: 2 },
                      sendMessage: { minArgs: 2, maxArgs: 3 },
                      setZoom: { minArgs: 1, maxArgs: 2 },
                      setZoomSettings: { minArgs: 1, maxArgs: 2 },
                      update: { minArgs: 1, maxArgs: 2 },
                    },
                    topSites: { get: { minArgs: 0, maxArgs: 0 } },
                    webNavigation: {
                      getAllFrames: { minArgs: 1, maxArgs: 1 },
                      getFrame: { minArgs: 1, maxArgs: 1 },
                    },
                    webRequest: {
                      handlerBehaviorChanged: { minArgs: 0, maxArgs: 0 },
                    },
                    windows: {
                      create: { minArgs: 0, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 1 },
                      getLastFocused: { minArgs: 0, maxArgs: 1 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                  };
                  if (0 === Object.keys(t).length)
                    throw new Error(
                      "api-metadata.json has not been included in browser-polyfill",
                    );
                  class o extends WeakMap {
                    constructor(e, n = void 0) {
                      (super(n), (this.createItem = e));
                    }
                    get(e) {
                      return (
                        this.has(e) || this.set(e, this.createItem(e)),
                        super.get(e)
                      );
                    }
                  }
                  const r = (e) =>
                      e && "object" == typeof e && "function" == typeof e.then,
                    i =
                      (n, t) =>
                      (...o) => {
                        e.runtime.lastError
                          ? n.reject(new Error(e.runtime.lastError.message))
                          : t.singleCallbackArg ||
                              (o.length <= 1 && !1 !== t.singleCallbackArg)
                            ? n.resolve(o[0])
                            : n.resolve(o);
                      },
                    a = (e) => (1 == e ? "argument" : "arguments"),
                    s = (e, n) =>
                      function (t, ...o) {
                        if (o.length < n.minArgs)
                          throw new Error(
                            `Expected at least ${n.minArgs} ${a(n.minArgs)} for ${e}(), got ${o.length}`,
                          );
                        if (o.length > n.maxArgs)
                          throw new Error(
                            `Expected at most ${n.maxArgs} ${a(n.maxArgs)} for ${e}(), got ${o.length}`,
                          );
                        return new Promise((r, a) => {
                          if (n.fallbackToNoCallback)
                            try {
                              t[e](...o, i({ resolve: r, reject: a }, n));
                            } catch (i) {
                              (console.warn(
                                `${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,
                                i,
                              ),
                                t[e](...o),
                                (n.fallbackToNoCallback = !1),
                                (n.noCallback = !0),
                                r());
                            }
                          else
                            n.noCallback
                              ? (t[e](...o), r())
                              : t[e](...o, i({ resolve: r, reject: a }, n));
                        });
                      },
                    d = (e, n, t) =>
                      new Proxy(n, { apply: (n, o, r) => t.call(o, e, ...r) });
                  let l = Function.call.bind(Object.prototype.hasOwnProperty);
                  const c = (e, n = {}, t = {}) => {
                      let o = Object.create(null),
                        r = {
                          has: (n, t) => t in e || t in o,
                          get(r, i, a) {
                            if (i in o) return o[i];
                            if (!(i in e)) return;
                            let g = e[i];
                            if ("function" == typeof g)
                              if ("function" == typeof n[i])
                                g = d(e, e[i], n[i]);
                              else if (l(t, i)) {
                                let n = s(i, t[i]);
                                g = d(e, e[i], n);
                              } else g = g.bind(e);
                            else if (
                              "object" == typeof g &&
                              null !== g &&
                              (l(n, i) || l(t, i))
                            )
                              g = c(g, n[i], t[i]);
                            else {
                              if (!l(t, "*"))
                                return (
                                  Object.defineProperty(o, i, {
                                    configurable: !0,
                                    enumerable: !0,
                                    get: () => e[i],
                                    set(n) {
                                      e[i] = n;
                                    },
                                  }),
                                  g
                                );
                              g = c(g, n[i], t["*"]);
                            }
                            return ((o[i] = g), g);
                          },
                          set: (n, t, r, i) => (
                            t in o ? (o[t] = r) : (e[t] = r),
                            !0
                          ),
                          defineProperty: (e, n, t) =>
                            Reflect.defineProperty(o, n, t),
                          deleteProperty: (e, n) =>
                            Reflect.deleteProperty(o, n),
                        },
                        i = Object.create(e);
                      return new Proxy(i, r);
                    },
                    g = (e) => ({
                      addListener(n, t, ...o) {
                        n.addListener(e.get(t), ...o);
                      },
                      hasListener: (n, t) => n.hasListener(e.get(t)),
                      removeListener(n, t) {
                        n.removeListener(e.get(t));
                      },
                    }),
                    u = new o((e) =>
                      "function" != typeof e
                        ? e
                        : function (n) {
                            const t = c(
                              n,
                              {},
                              { getContent: { minArgs: 0, maxArgs: 0 } },
                            );
                            e(t);
                          },
                    ),
                    m = new o((e) =>
                      "function" != typeof e
                        ? e
                        : function (n, t, o) {
                            let i,
                              a,
                              s = !1,
                              d = new Promise((e) => {
                                i = function (n) {
                                  ((s = !0), e(n));
                                };
                              });
                            try {
                              a = e(n, t, i);
                            } catch (e) {
                              a = Promise.reject(e);
                            }
                            const l = !0 !== a && r(a);
                            if (!0 !== a && !l && !s) return !1;
                            const c = (e) => {
                              e.then(
                                (e) => {
                                  o(e);
                                },
                                (e) => {
                                  let n;
                                  ((n =
                                    e &&
                                    (e instanceof Error ||
                                      "string" == typeof e.message)
                                      ? e.message
                                      : "An unexpected error occurred"),
                                    o({
                                      __mozWebExtensionPolyfillReject__: !0,
                                      message: n,
                                    }));
                                },
                              ).catch((e) => {
                                console.error(
                                  "Failed to send onMessage rejected reply",
                                  e,
                                );
                              });
                            };
                            return (c(l ? a : d), !0);
                          },
                    ),
                    f = ({ reject: t, resolve: o }, r) => {
                      e.runtime.lastError
                        ? e.runtime.lastError.message === n
                          ? o()
                          : t(new Error(e.runtime.lastError.message))
                        : r && r.__mozWebExtensionPolyfillReject__
                          ? t(new Error(r.message))
                          : o(r);
                    },
                    p = (e, n, t, ...o) => {
                      if (o.length < n.minArgs)
                        throw new Error(
                          `Expected at least ${n.minArgs} ${a(n.minArgs)} for ${e}(), got ${o.length}`,
                        );
                      if (o.length > n.maxArgs)
                        throw new Error(
                          `Expected at most ${n.maxArgs} ${a(n.maxArgs)} for ${e}(), got ${o.length}`,
                        );
                      return new Promise((e, n) => {
                        const r = f.bind(null, { resolve: e, reject: n });
                        (o.push(r), t.sendMessage(...o));
                      });
                    },
                    w = {
                      devtools: { network: { onRequestFinished: g(u) } },
                      runtime: {
                        onMessage: g(m),
                        onMessageExternal: g(m),
                        sendMessage: p.bind(null, "sendMessage", {
                          minArgs: 1,
                          maxArgs: 3,
                        }),
                      },
                      tabs: {
                        sendMessage: p.bind(null, "sendMessage", {
                          minArgs: 2,
                          maxArgs: 3,
                        }),
                      },
                    },
                    h = {
                      clear: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    };
                  return (
                    (t.privacy = {
                      network: { "*": h },
                      services: { "*": h },
                      websites: { "*": h },
                    }),
                    c(e, w, t)
                  );
                };
              e.exports = t(chrome);
            }
          }),
          void 0 === (r = "function" == typeof t ? t.apply(n, o) : t) ||
            (e.exports = r));
      },
    },
    n = {};
  function t(o) {
    var r = n[o];
    if (void 0 !== r) return r.exports;
    var i = (n[o] = { exports: {} });
    return (e[o].call(i.exports, i, i.exports, t), i.exports);
  }
  ((t.n = (e) => {
    var n = e && e.__esModule ? () => e.default : () => e;
    return (t.d(n, { a: n }), n);
  }),
    (t.d = (e, n) => {
      for (var o in n)
        t.o(n, o) &&
          !t.o(e, o) &&
          Object.defineProperty(e, o, { enumerable: !0, get: n[o] });
    }),
    (t.o = (e, n) => Object.prototype.hasOwnProperty.call(e, n)),
    (() => {
      "use strict";
      function e(e, n, t, o) {
        return new (t || (t = Promise))(function (r, i) {
          function a(e) {
            try {
              d(o.next(e));
            } catch (e) {
              i(e);
            }
          }
          function s(e) {
            try {
              d(o.throw(e));
            } catch (e) {
              i(e);
            }
          }
          function d(e) {
            var n;
            e.done
              ? r(e.value)
              : ((n = e.value),
                n instanceof t
                  ? n
                  : new t(function (e) {
                      e(n);
                    })).then(a, s);
          }
          d((o = o.apply(e, n || [])).next());
        });
      }
      Object.create;
      Object.create;
      var n,
        o,
        r,
        i,
        a,
        s,
        d,
        l,
        c,
        g,
        u,
        m,
        f = t(150),
        p = t.n(f);
      (!(function (e) {
        ((e[(e.pre = 0)] = "pre"),
          (e[(e.after = 1)] = "after"),
          (e[(e.getExtDrmKey = 2)] = "getExtDrmKey"));
      })(n || (n = {})),
        (function (e) {
          ((e[(e.single = 0)] = "single"),
            (e[(e.bulk = 1)] = "bulk"),
            (e[(e.bloburl = 2)] = "bloburl"),
            (e[(e.changeUrl = 3)] = "changeUrl"),
            (e[(e.login = 4)] = "login"),
            (e[(e.googleLogin = 5)] = "googleLogin"),
            (e[(e.register = 6)] = "register"),
            (e[(e.sendEmailCode = 7)] = "sendEmailCode"),
            (e[(e.getDrmSecretKey = 8)] = "getDrmSecretKey"),
            (e[(e.getConfig = 9)] = "getConfig"),
            (e[(e.getMemberInfo = 10)] = "getMemberInfo"),
            (e[(e.updateNoPerDayDownloadCount = 11)] =
              "updateNoPerDayDownloadCount"));
        })(o || (o = {})),
        (function (e) {
          ((e[(e.goSubscribe = 0)] = "goSubscribe"),
            (e[(e.pureNotice = 1)] = "pureNotice"),
            (e[(e.drmLicense = 2)] = "drmLicense"),
            (e[(e.retryMessage = 3)] = "retryMessage"),
            (e[(e.serverError = 4)] = "serverError"));
        })(r || (r = {})),
        (function (e) {
          ((e[(e.Edge = 0)] = "Edge"),
            (e[(e.Chrome = 1)] = "Chrome"),
            (e[(e.Firefox = 2)] = "Firefox"),
            (e[(e.Opera = 3)] = "Opera"),
            (e[(e.Safari = 4)] = "Safari"),
            (e[(e.Unknown = 5)] = "Unknown"));
        })(i || (i = {})),
        (function (e) {
          ((e.default = "log"), (e.warn = "warn"), (e.error = "error"));
        })(a || (a = {})),
        (function (e) {
          ((e.install = "install"),
            (e.uninstall = "uninstall"),
            (e.downloadSignalUnkown = "downloadSignalUnkown"),
            (e.downloadSignalImg = "downloadSignalImg"),
            (e.downloadSignalVideo = "downloadSignalVideo"),
            (e.downloadBulk = "downloadBulk"),
            (e.changeUrl = "changeUrl"),
            (e.register = "register"),
            (e.login = "login"),
            (e.googleLogin = "googleLogin"),
            (e.sendEmailCode = "sendEmailCode"),
            (e.uploadFiles = "uploadFiles"),
            (e.concatVideoAndAudio = "concatVideoAndAudio"));
        })(s || (s = {})),
        (function (e) {
          ((e.downloadSuccess = "downloadSuccess"),
            (e.downloadError = "downloadError"),
            (e.downloadCancle = "downloadCancle"),
            (e.downloadWating = "downloadWating"),
            (e.downloadPrepare = "downloadPrepare"),
            (e.downloadStuck = "downloadStuck"));
        })(d || (d = {})),
        (function (e) {
          ((e.addOrUpdateDownloadingInfo = "addOrUpdateDownloadingInfo"),
            (e.updateDownloadStatus = "updateDownloadStatus"));
        })(l || (l = {})),
        (function (e) {
          e[(e.refresh = 0)] = "refresh";
        })(c || (c = {})),
        (function (e) {
          ((e.downloading = "downloading"),
            (e.downloaded = "downloaded"),
            (e.download = "download"),
            (e.all = "all"),
            (e.quota = "quota"));
        })(g || (g = {})),
        (function (e) {
          ((e.processVideo = "processVideo"),
            (e.processVideoInWeb = "processVideoInWeb"),
            (e.processVideoByUrl = "processVideoByUrl"));
        })(u || (u = {})),
        (function (e) {
          ((e.serverError = "serverError"), (e.tip = "tip"));
        })(m || (m = {})));
      var w;
      !(function (n) {
        let t;
        const o = () => {
          var e;
          null === (e = document.getElementById("addOnInfoWrapperid")) ||
            void 0 === e ||
            e.remove();
          const n = document.createElement("div");
          return (
            (n.id = "addOnInfoWrapperid"),
            (n.innerHTML =
              '\n    <div class="modal" id="modal">\n        <div class="modal-header">\n            <div id="addon-info-title"></div>\n        </div>\n        <div class="modal-content">\n        </div>\n        <div class="modal-footer">            \n        </div>\n    </div>\n    '),
            document.body.appendChild(n),
            n
          );
        };
        function i(e, n, t) {
          const o = document.createElement("button");
          return (
            o.classList.add("btn", e),
            (o.textContent = n),
            o.addEventListener("click", function () {
              (null != t && t(), s());
            }),
            o
          );
        }
        function a() {
          const e = document.getElementById("modal");
          ((e.style.display = "block"),
            setTimeout(() => {
              e.classList.add("show");
            }, 10));
        }
        function s() {
          const e = document.getElementById("modal");
          (e.classList.remove("show"),
            setTimeout(() => {
              ((e.style.display = "none"), clearInterval(t));
            }, 300));
        }
        ((n.displayMessage = function (e, t = 10) {
          T(location.href) &&
            (e.type == r.goSubscribe
              ? (console.log(e),
                console.log(e.mainAction),
                e.mainAction && "blank" == e.mainAction
                  ? (console.log(111),
                    n.openModalWithButton(
                      e.title,
                      e.text,
                      e.mainText,
                      () => {
                        window.open(e.mainUrl, "_blank");
                      },
                      e.subText,
                      null,
                    ))
                  : n.openModalWithButton(
                      e.title,
                      e.text,
                      e.mainText,
                      () => {
                        window.location.href = e.mainUrl;
                      },
                      e.subText,
                      null,
                    ))
              : e.type == r.pureNotice &&
                n.openModalWithTimer(e.title, e.text, t));
        }),
          (n.openModalWithTimer = function (n, r, i = 10) {
            if (T(location.href)) {
              o();
              const d = document.getElementById("addon-info-title"),
                l = document.querySelector(".modal-content"),
                c = document.querySelector(".modal-footer");
              (clearInterval(t),
                (l.innerHTML = r),
                (c.innerHTML = ""),
                (d.innerHTML = n));
              const g = document.createElement("div");
              ((g.id = "countdown"),
                (g.textContent = `close in ${i}s`),
                c.appendChild(g));
              let u = i;
              t = setInterval(() => {
                u <= 0
                  ? (clearInterval(t), s())
                  : ((g.textContent = `close in ${u}s`), u--);
              }, 1e3);
              const m = document.querySelector(".noticButtonP .noticA");
              (m &&
                (m.onclick = () =>
                  e(this, void 0, void 0, function* () {
                    let e = yield O("discordUrl");
                    (window.open(e + "", "_blank"),
                      yield P("isJumpDiscord", !0));
                  })),
                a());
            }
          }),
          (n.openModalWithButton = function (e, n, r, s, d, l) {
            if (T(location.href)) {
              o();
              const c = document.getElementById("addon-info-title"),
                g = document.querySelector(".modal-content"),
                u = document.querySelector(".modal-footer");
              if (
                (clearInterval(t),
                (g.innerHTML = n),
                (u.innerHTML = ""),
                (c.innerHTML = e),
                null != r)
              ) {
                const e = i("btn-wishlist", r, s);
                u.appendChild(e);
              }
              if (null != d) {
                const e = i("btn-no-thanks", d, l);
                u.appendChild(e);
              }
              const m = document.querySelector(".notice_openSiderpanle");
              (m &&
                (m.onclick = () => {
                  let e = p().runtime.connect({ name: "openSidepanels" });
                  (e.postMessage({}), e.disconnect());
                }),
                a());
            }
          }));
      })(w || (w = {}));
      var h = (function () {
        var e = "undefined" != typeof self ? self : this,
          n = {
            navigator: void 0 !== e.navigator ? e.navigator : {},
            infoMap: {
              engine: ["WebKit", "Trident", "Gecko", "Presto"],
              browser: [
                "Safari",
                "Chrome",
                "Edge",
                "IE",
                "Firefox",
                "Firefox Focus",
                "Chromium",
                "Opera",
                "Vivaldi",
                "Yandex",
                "Arora",
                "Lunascape",
                "QupZilla",
                "Coc Coc",
                "Kindle",
                "Iceweasel",
                "Konqueror",
                "Iceape",
                "SeaMonkey",
                "Epiphany",
                "360",
                "360SE",
                "360EE",
                "UC",
                "QQBrowser",
                "QQ",
                "Baidu",
                "Maxthon",
                "Sogou",
                "LBBROWSER",
                "2345Explorer",
                "TheWorld",
                "XiaoMi",
                "Quark",
                "Qiyu",
                "Wechat",
                ,
                "WechatWork",
                "Taobao",
                "Alipay",
                "Weibo",
                "Douban",
                "Suning",
                "iQiYi",
              ],
              os: [
                "Windows",
                "Linux",
                "Mac OS",
                "Android",
                "Ubuntu",
                "FreeBSD",
                "Debian",
                "iOS",
                "Windows Phone",
                "BlackBerry",
                "MeeGo",
                "Symbian",
                "Chrome OS",
                "WebOS",
              ],
              device: ["Mobile", "Tablet", "iPad"],
            },
          },
          t = {
            createUUID: function () {
              for (var e = [], n = "0123456789abcdef", t = 0; t < 36; t++)
                e[t] = n.substr(Math.floor(16 * Math.random()), 1);
              return (
                (e[14] = "4"),
                (e[19] = n.substr((3 & e[19]) | 8, 1)),
                (e[8] = e[13] = e[18] = e[23] = "-"),
                e.join("")
              );
            },
            getDate: function () {
              var e = new Date(),
                n = e.getFullYear(),
                t = e.getMonth() + 1,
                o = e.getDate(),
                r = e.getHours(),
                i = e.getMinutes(),
                a = e.getSeconds();
              return ""
                .concat(n.toString(), "/")
                .concat(t.toString(), "/")
                .concat(o.toString(), " ")
                .concat(r.toString(), ":")
                .concat(i.toString(), ":")
                .concat(a.toString());
            },
            getTimezoneOffset: function () {
              return new Date().getTimezoneOffset();
            },
            getTimezone: function () {
              return Intl.DateTimeFormat().resolvedOptions().timeZone;
            },
            getMatchMap: function (e) {
              return {
                Trident: e.indexOf("Trident") > -1 || e.indexOf("NET CLR") > -1,
                Presto: e.indexOf("Presto") > -1,
                WebKit: e.indexOf("AppleWebKit") > -1,
                Gecko: e.indexOf("Gecko/") > -1,
                Safari: e.indexOf("Safari") > -1,
                Chrome: e.indexOf("Chrome") > -1 || e.indexOf("CriOS") > -1,
                IE: e.indexOf("MSIE") > -1 || e.indexOf("Trident") > -1,
                Edge: e.indexOf("Edge") > -1,
                Firefox: e.indexOf("Firefox") > -1 || e.indexOf("FxiOS") > -1,
                "Firefox Focus": e.indexOf("Focus") > -1,
                Chromium: e.indexOf("Chromium") > -1,
                Opera: e.indexOf("Opera") > -1 || e.indexOf("OPR") > -1,
                Vivaldi: e.indexOf("Vivaldi") > -1,
                Yandex: e.indexOf("YaBrowser") > -1,
                Arora: e.indexOf("Arora") > -1,
                Lunascape: e.indexOf("Lunascape") > -1,
                QupZilla: e.indexOf("QupZilla") > -1,
                "Coc Coc": e.indexOf("coc_coc_browser") > -1,
                Kindle: e.indexOf("Kindle") > -1 || e.indexOf("Silk/") > -1,
                Iceweasel: e.indexOf("Iceweasel") > -1,
                Konqueror: e.indexOf("Konqueror") > -1,
                Iceape: e.indexOf("Iceape") > -1,
                SeaMonkey: e.indexOf("SeaMonkey") > -1,
                Epiphany: e.indexOf("Epiphany") > -1,
                360:
                  e.indexOf("QihooBrowser") > -1 || e.indexOf("QHBrowser") > -1,
                "360EE": e.indexOf("360EE") > -1,
                "360SE": e.indexOf("360SE") > -1,
                UC: e.indexOf("UC") > -1 || e.indexOf(" UBrowser") > -1,
                QQBrowser: e.indexOf("QQBrowser") > -1,
                QQ: e.indexOf("QQ/") > -1,
                Baidu: e.indexOf("Baidu") > -1 || e.indexOf("BIDUBrowser") > -1,
                Maxthon: e.indexOf("Maxthon") > -1,
                Sogou: e.indexOf("MetaSr") > -1 || e.indexOf("Sogou") > -1,
                LBBROWSER:
                  e.indexOf("LBBROWSER") > -1 || e.indexOf("LieBaoFast") > -1,
                "2345Explorer": e.indexOf("2345Explorer") > -1,
                TheWorld: e.indexOf("TheWorld") > -1,
                XiaoMi: e.indexOf("MiuiBrowser") > -1,
                Quark: e.indexOf("Quark") > -1,
                Qiyu: e.indexOf("Qiyu") > -1,
                Wechat: e.indexOf("MicroMessenger") > -1,
                WechatWork: e.indexOf("wxwork/") > -1,
                Taobao: e.indexOf("AliApp(TB") > -1,
                Alipay: e.indexOf("AliApp(AP") > -1,
                Weibo: e.indexOf("Weibo") > -1,
                Douban: e.indexOf("com.douban.frodo") > -1,
                Suning: e.indexOf("SNEBUY-APP") > -1,
                iQiYi: e.indexOf("IqiyiApp") > -1,
                DingTalk: e.indexOf("DingTalk") > -1,
                Vivo: e.indexOf("VivoBrowser") > -1,
                Huawei:
                  e.indexOf("HuaweiBrowser") > -1 ||
                  e.indexOf("HUAWEI/") > -1 ||
                  e.indexOf("HONOR") > -1 ||
                  e.indexOf("HBPC/") > -1,
                Windows: e.indexOf("Windows") > -1,
                Linux: e.indexOf("Linux") > -1 || e.indexOf("X11") > -1,
                "Mac OS": e.indexOf("Macintosh") > -1,
                Android: e.indexOf("Android") > -1 || e.indexOf("Adr") > -1,
                Ubuntu: e.indexOf("Ubuntu") > -1,
                FreeBSD: e.indexOf("FreeBSD") > -1,
                Debian: e.indexOf("Debian") > -1,
                "Windows Phone":
                  e.indexOf("IEMobile") > -1 || e.indexOf("Windows Phone") > -1,
                BlackBerry:
                  e.indexOf("BlackBerry") > -1 || e.indexOf("RIM") > -1,
                MeeGo: e.indexOf("MeeGo") > -1,
                Symbian: e.indexOf("Symbian") > -1,
                iOS: e.indexOf("like Mac OS X") > -1,
                "Chrome OS": e.indexOf("CrOS") > -1,
                WebOS: e.indexOf("hpwOS") > -1,
                Mobile:
                  e.indexOf("Mobi") > -1 ||
                  e.indexOf("iPh") > -1 ||
                  e.indexOf("480") > -1,
                Tablet: e.indexOf("Tablet") > -1 || e.indexOf("Nexus 7") > -1,
                iPad: e.indexOf("iPad") > -1,
              };
            },
            matchInfoMap: function (e) {
              var o = n.navigator.userAgent || {},
                r = t.getMatchMap(o);
              for (var i in n.infoMap)
                for (var a = 0; a < n.infoMap[i].length; a++) {
                  var s = n.infoMap[i][a];
                  r[s] && (e[i] = s);
                }
            },
            getOS: function () {
              return (t.matchInfoMap(this), this.os);
            },
            getOSVersion: function () {
              var e = this,
                t = n.navigator.userAgent || {};
              e.osVersion = "";
              var o = {
                Windows: function () {
                  var e = t.replace(/^.*Windows NT ([\d.]+);.*$/, "$1");
                  return (
                    {
                      10: "10 || 11",
                      6.3: "8.1",
                      6.2: "8",
                      6.1: "7",
                      "6.0": "Vista",
                      5.2: "XP 64-Bit",
                      5.1: "XP",
                      "5.0": "2000",
                      "4.0": "NT 4.0",
                      "3.5.1": "NT 3.5.1",
                      3.5: "NT 3.5",
                      3.1: "NT 3.1",
                    }[e] || e
                  );
                },
                Android: function () {
                  return t.replace(/^.*Android ([\d.]+);.*$/, "$1");
                },
                iOS: function () {
                  return t
                    .replace(/^.*OS ([\d_]+) like.*$/, "$1")
                    .replace(/_/g, ".");
                },
                Debian: function () {
                  return t.replace(/^.*Debian\/([\d.]+).*$/, "$1");
                },
                "Windows Phone": function () {
                  return t.replace(/^.*Windows Phone( OS)? ([\d.]+);.*$/, "$2");
                },
                "Mac OS": function () {
                  return t
                    .replace(/^.*Mac OS X ([\d_]+).*$/, "$1")
                    .replace(/_/g, ".");
                },
                WebOS: function () {
                  return t.replace(/^.*hpwOS\/([\d.]+);.*$/, "$1");
                },
              };
              return (
                o[e.os] &&
                  ((e.osVersion = o[e.os]()),
                  e.osVersion == t && (e.osVersion = "")),
                e.osVersion
              );
            },
            getDeviceType: function () {
              var e = this;
              return ((e.device = "PC"), t.matchInfoMap(e), e.device);
            },
            getNetwork: function () {
              return "";
            },
            getLanguage: function () {
              var e;
              return (
                (this.language =
                  ((e = (
                    n.navigator.browserLanguage || n.navigator.language
                  ).split("-"))[1] && (e[1] = e[1].toUpperCase()),
                  e.join("_"))),
                this.language
              );
            },
            getBrowserInfo: function () {
              var e = this;
              t.matchInfoMap(e);
              var o = n.navigator.userAgent || {},
                r = t.getMatchMap(o);
              if (
                (r.Baidu && r.Opera && (r.Baidu = !1),
                r.Mobile && (r.Mobile = !(o.indexOf("iPad") > -1)),
                r.IE || r.Edge)
              )
                switch (window.screenTop - window.screenY) {
                  case 71:
                  case 74:
                  case 99:
                  case 75:
                  case 74:
                  case 105:
                  default:
                    break;
                  case 102:
                    r["360EE"] = !0;
                    break;
                  case 104:
                    r["360SE"] = !0;
                }
              var i = {
                Safari: function () {
                  return o.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Chrome: function () {
                  return o
                    .replace(/^.*Chrome\/([\d.]+).*$/, "$1")
                    .replace(/^.*CriOS\/([\d.]+).*$/, "$1");
                },
                IE: function () {
                  return o
                    .replace(/^.*MSIE ([\d.]+).*$/, "$1")
                    .replace(/^.*rv:([\d.]+).*$/, "$1");
                },
                Edge: function () {
                  return o.replace(/^.*Edge\/([\d.]+).*$/, "$1");
                },
                Firefox: function () {
                  return o
                    .replace(/^.*Firefox\/([\d.]+).*$/, "$1")
                    .replace(/^.*FxiOS\/([\d.]+).*$/, "$1");
                },
                "Firefox Focus": function () {
                  return o.replace(/^.*Focus\/([\d.]+).*$/, "$1");
                },
                Chromium: function () {
                  return o.replace(/^.*Chromium\/([\d.]+).*$/, "$1");
                },
                Opera: function () {
                  return o
                    .replace(/^.*Opera\/([\d.]+).*$/, "$1")
                    .replace(/^.*OPR\/([\d.]+).*$/, "$1");
                },
                Vivaldi: function () {
                  return o.replace(/^.*Vivaldi\/([\d.]+).*$/, "$1");
                },
                Yandex: function () {
                  return o.replace(/^.*YaBrowser\/([\d.]+).*$/, "$1");
                },
                Arora: function () {
                  return o.replace(/^.*Arora\/([\d.]+).*$/, "$1");
                },
                Lunascape: function () {
                  return o.replace(/^.*Lunascape[\/\s]([\d.]+).*$/, "$1");
                },
                QupZilla: function () {
                  return o.replace(/^.*QupZilla[\/\s]([\d.]+).*$/, "$1");
                },
                "Coc Coc": function () {
                  return o.replace(/^.*coc_coc_browser\/([\d.]+).*$/, "$1");
                },
                Kindle: function () {
                  return o.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Iceweasel: function () {
                  return o.replace(/^.*Iceweasel\/([\d.]+).*$/, "$1");
                },
                Konqueror: function () {
                  return o.replace(/^.*Konqueror\/([\d.]+).*$/, "$1");
                },
                Iceape: function () {
                  return o.replace(/^.*Iceape\/([\d.]+).*$/, "$1");
                },
                SeaMonkey: function () {
                  return o.replace(/^.*SeaMonkey\/([\d.]+).*$/, "$1");
                },
                Epiphany: function () {
                  return o.replace(/^.*Epiphany\/([\d.]+).*$/, "$1");
                },
                Maxthon: function () {
                  return o.replace(/^.*Maxthon\/([\d.]+).*$/, "$1");
                },
              };
              return (
                (e.browserVersion = ""),
                i[e.browser] &&
                  ((e.browserVersion = i[e.browser]()),
                  e.browserVersion == o && (e.browserVersion = "")),
                "Chrome" == e.browser &&
                  o.match(/\S+Browser/) &&
                  ((e.browser = o.match(/\S+Browser/)[0]),
                  (e.version = o.replace(/^.*Browser\/([\d.]+).*$/, "$1"))),
                "Edge" == e.browser &&
                  (e.version > "75"
                    ? (e.engine = "Blink")
                    : (e.engine = "EdgeHTML")),
                (("Chrome" == e.browser && parseInt(e.browserVersion) > 27) ||
                  (r.Chrome &&
                    "WebKit" == e.engine &&
                    parseInt(i.Chrome()) > 27) ||
                  ("Opera" == e.browser && parseInt(e.version) > 12) ||
                  "Yandex" == e.browser) &&
                  (e.engine = "Blink"),
                e.browser +
                  "(version: " +
                  e.browserVersion +
                  "&nbsp;&nbsp;kernel: " +
                  e.engine +
                  ")"
              );
            },
            getGeoPostion: function () {
              return new Promise(function (e, n) {
                navigator && navigator.geolocation
                  ? navigator.geolocation.getCurrentPosition(
                      function (n) {
                        e(n);
                      },
                      function () {
                        e({ coords: { longitude: "fail", latitude: "fail" } });
                      },
                      { enableHighAccuracy: !1, timeout: 1e4 },
                    )
                  : n("fail");
              });
            },
            getPlatform: function () {
              return (
                (n.navigator.userAgentData &&
                  n.navigator.userAgentData.platform) ||
                n.navigator.platform
              );
            },
          },
          o = {
            DeviceInfoObj: function (e) {
              var o = {
                  deviceType: t.getDeviceType(),
                  os: t.getOS(),
                  osVersion: t.getOSVersion(),
                  platform: t.getPlatform(),
                  language: t.getLanguage(),
                  network: t.getNetwork(),
                  browserInfo: t.getBrowserInfo(),
                  userAgent: n.navigator.userAgent,
                  geoPosition: !0,
                  date: t.getDate(),
                  timezoneOffset: t.getTimezoneOffset(),
                  timezone: t.getTimezone(),
                  uuid: t.createUUID(),
                },
                r = {};
              if (e && e.info && 0 !== e.info.length) {
                var i = {},
                  a = function (n) {
                    e.info.forEach(function (e) {
                      e.toLowerCase() === n.toLowerCase() &&
                        (i[(e = n)] = o[e]);
                    });
                  };
                for (var s in o) a(s);
                r = i;
              } else r = o;
              return r;
            },
          };
        return {
          Info: function (e) {
            return o.DeviceInfoObj(e);
          },
        };
      })();
      function A() {
        return h.Info({
          info: [
            "deviceType",
            "OS",
            "OSVersion",
            "platform",
            "language",
            "netWork",
            "browserInfo",
            "screenHeight",
            "screenWidth",
            "userAgent",
            "appCodeName",
            "appName",
            "appVersion",
            "geoPosition",
            "date",
            "UUID",
            "timezoneOffset",
            "timezone",
          ],
        });
      }
      class x {
        static userReg(e, n, t, o, r, i) {
          const a = new FormData();
          let s = A();
          for (const e in s) a.append(e, s[e]);
          (a.append("userId", e),
            a.append("extId", n),
            a.append("version", t),
            a.append("action", o),
            a.append("detail", JSON.stringify(r)),
            console.log(
              "fetch url:https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-1",
            ),
            fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-1",
              { method: "POST", body: a },
            ).then((e) => {
              i && i(e);
            }));
        }
        static logAction(n, t, o, r, i, a, s) {
          return e(this, void 0, void 0, function* () {
            console.log("====start log verion:" + o);
            const e = new FormData();
            let d = A();
            for (const n in d) e.append(n, d[n]);
            (e.append("userId", n),
              e.append("extId", t),
              e.append("version", o),
              e.append("action", r),
              e.append("detail", JSON.stringify(i)),
              a &&
                (e.append("url", a), e.append("domain", new URL(a).hostname)));
            let l = null;
            try {
              let n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/log-action-1",
                { method: "POST", body: e },
              );
              (s && s(n), (l = n.json()));
            } catch (e) {}
            return l;
          });
        }
        static userReg2(n, t, o, r, i, a, s, d) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            let l = A();
            for (const n in l) e.append(n, l[n]);
            (e.append("userId", n),
              e.append("extId", t),
              e.append("version", o),
              e.append("action", r),
              e.append("email", i),
              e.append("password", a),
              e.append("emailCode", s),
              e.append("detail", JSON.stringify(d)));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-2",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static userLogin(n, t, o, r, i, a, s) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            let d = A();
            for (const n in d) e.append(n, d[n]);
            (e.append("userId", n),
              e.append("extId", t),
              e.append("version", o),
              e.append("action", r),
              e.append("email", i),
              e.append("password", a),
              e.append("detail", JSON.stringify(s)));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/login",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static sendEmailCode(n, t, o, r, i, a) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            let s = A();
            for (const n in s) e.append(n, s[n]);
            (e.append("userId", n),
              e.append("extId", t),
              e.append("version", o),
              e.append("action", r),
              e.append("email", i),
              e.append("detail", JSON.stringify(a)));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/getEmailCode",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getSecretKeyPre(n) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            e.append("pssh", n);
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getSecretKeyPre",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              const t = yield n.json();
              return 0 == t.code
                ? { success: !0, data: t.data }
                : { success: !1, error: t.msg };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getSecretKeyAfter(n, t, o, r) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("session_id", n), e.append("licence", t));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getSecretKeyAfter",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              const t = yield n.json();
              return 0 == t.code
                ? (yield this.saveDrmKey(
                    "a5376339-a50f-f096-248c-0e0cdde4f9af",
                    o,
                    t.data.keys.trim(),
                    r,
                  ),
                  { success: !0, data: t.data })
                : { success: !1, error: t.msg };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static checkHasVideo(n, t) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("extId", n), e.append("courseId", t));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/file/ude/checkHasVideo",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getExtDrmKey(n, t) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("extId", n), e.append("courseId", t));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getExtDrmKey",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static saveDrmKey(n, t, o, r) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("extId", n),
              e.append("courseId", t),
              e.append("drmKey", o),
              e.append("userId", r));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/saveDrmKey",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getConfig() {
          return e(this, void 0, void 0, function* () {
            let e = "",
              n = !0;
            try {
              const t = new FormData();
              t.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af");
              const o = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/getConfig",
                { method: "POST", body: t },
              );
              if (o.ok) {
                e = (yield o.json()).data;
              } else n = !1;
            } catch (e) {
              ((n = !1), console.log(e));
            }
            var t;
            return (
              !n && this.retryCount < this.retryMaxCount
                ? (this.retryCount++,
                  yield ((t = 1e3), new Promise((e) => setTimeout(e, t))),
                  this.getConfig())
                : (this.retryCount = 0),
              e
            );
          });
        }
        static getUserMemberInfo(n, t) {
          return e(this, void 0, void 0, function* () {
            let e = "";
            try {
              const o = new FormData();
              let r = A();
              for (const e in r) o.append(e, r[e]);
              (o.append("userId", n),
                o.append("version", t),
                o.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"));
              const i = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/member/ude/getUserMemberInfoNew",
                { method: "POST", body: o },
              );
              if (i.ok) {
                e = (yield i.json()).data;
              }
            } catch (e) {
              console.log(e);
            }
            return e;
          });
        }
        static getDownloadCount(n) {
          return e(this, void 0, void 0, function* () {
            let e = null;
            try {
              const t = new FormData();
              let o = A();
              for (const e in o) t.append(e, o[e]);
              (t.append("userId", n),
                t.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"));
              const r = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/getDownloadCount",
                { method: "POST", body: t },
              );
              if (r.ok) {
                const n = yield r.json();
                0 === n.code && (e = n.data);
              }
            } catch (e) {
              console.log(e);
            }
            return e;
          });
        }
        static updateNoPerDayDownloadCount(n) {
          return e(this, void 0, void 0, function* () {
            try {
              const e = new FormData();
              let t = A();
              for (const n in t) e.append(n, t[n]);
              (e.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"),
                e.append("detail", n));
              const o = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/member/ude/updateNoPerDayDownloadCount",
                { method: "POST", body: e },
              );
              if (o.ok) {
                0 === (yield o.json()).code &&
                  console.log("update downloadCount success");
              }
            } catch (e) {
              console.log(e);
            }
          });
        }
        static downloadRecord(n, t) {
          return e(this, void 0, void 0, function* () {
            try {
              const e = new FormData();
              let o = A();
              for (const n in o) e.append(n, o[n]);
              (e.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"),
                e.append("userId", n),
                e.append("version", p().runtime.getManifest().version));
              let r = JSON.stringify({ perday: t });
              e.append("detail", r);
              const i = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/record/ude/downloadRecord",
                { method: "POST", body: e },
              );
              if (i.ok) {
                0 === (yield i.json()).code &&
                  console.log("downloadRecord success");
              }
            } catch (e) {
              console.log(e);
            }
          });
        }
      }
      ((x.retryCount = 0), (x.retryMaxCount = 3));
      const y = "udemyDownloadingInfo",
        v = "udemyDownloadInfo",
        b = "udemyDownloadedInfo";
      function I() {
        return e(this, void 0, void 0, function* () {
          let e = yield O("UdemyDownloaderUserInfo");
          return null != e && null != e.userId && "" != e.userId ? e : null;
        });
      }
      function O(e) {
        return p()
          .storage.local.get(e)
          .then((n) => n[e]);
      }
      function P(e, n) {
        return p().storage.local.set({ [e]: n });
      }
      function C(n) {
        return e(this, void 0, void 0, function* () {
          let t = !1,
            r = !1,
            i = "count",
            a = 0,
            s = yield (function () {
              return e(this, void 0, void 0, function* () {
                let e = !1,
                  n = (yield I()).canBusiness,
                  t =
                    ((o = location.href),
                    !"www.udemy.com,downloadsp.html,service_work.js,service_work_firefox.js"
                      .split(",")
                      .some((e) => o.includes(e)));
                var o;
                return ("1" != n && t && (e = !0), e);
              });
            })(),
            d = yield (function () {
              return e(this, void 0, void 0, function* () {
                let e = parseInt("2");
                try {
                  let e = {
                    videoUrlList: [],
                    videoNameList: [],
                    type: o.getMemberInfo,
                  };
                  yield p().runtime.sendMessage(e);
                } catch (e) {
                } finally {
                  let n = yield I(),
                    t = null == n.maxDownloadCount ? 0 : n.maxDownloadCount,
                    o = n.noPerDayMemberList,
                    r = 0;
                  if (null != o && o.length > 0)
                    for (let e = 0; e < o.length; e++) {
                      let n = yield O(o[e].id + ""),
                        t = null == n ? 0 : n,
                        i =
                          null == o[e].downloadedCount
                            ? 0
                            : o[e].downloadedCount;
                      i > t && ((t = i), yield P(o[e].id + "", t));
                      let a = o[e].downloadCount - t;
                      ((a = a < 0 ? 0 : a), (r += a));
                    }
                  e = t + r;
                }
                return e;
              });
            })(),
            l = yield O("downloadCount");
          if (-2 == l) ((t = !0), (r = !0));
          else {
            let e = yield I(),
              o = e.maxDownloadCount < 0 ? 0 : e.maxDownloadCount;
            ((l = l < 0 ? 0 : l),
              l > e.maxDownloadCount && (l = o),
              console.log(l, d));
            let r = (yield O(y)) || [],
              c = (yield O(v)) || [];
            (s || l + r.length + c.length + n >= d) &&
              ((t = !0),
              s
                ? ((i = "business"), (a = 0))
                : (a = d - (l + r.length + c.length)));
          }
          return {
            isNeedLogin: t,
            type: i,
            canDownloadCount: a,
            serverIsError: r,
          };
        });
      }
      function S(n) {
        return e(this, void 0, void 0, function* () {
          S && (yield P("maxConcurrentDownloads", n));
        });
      }
      new (class {
        constructor() {
          this.locks = {};
        }
        acquire(n, t) {
          return e(this, void 0, void 0, function* () {
            const e = (this.locks[n] || Promise.resolve())
              .then(t)
              .catch(() => {});
            return ((this.locks[n] = e), e);
          });
        }
      })();
      function D(n) {
        return e(this, void 0, void 0, function* () {
          let e = yield O(v);
          e || (e = []);
          const t = e.findIndex((e) => e.downloadId === n);
          -1 !== t && (e.splice(t, 1), yield P(v, e));
        });
      }
      function E(n, t, o) {
        return e(this, void 0, void 0, function* () {
          yield D(n.downloadId);
          let r = {
            downloadId: n.downloadId,
            courseId: n.courseId,
            lectureId: n.lectureId,
            videoName: n.filenameOutput,
            downloadTime: n.downloadTime,
            secretKey: n.secretKey,
            webUrl: n.webUrl,
            downloadMethod: n.downloadMethod,
            status: t,
            msg: o,
            fileUrl: "",
          };
          yield (function (n) {
            return e(this, void 0, void 0, function* () {
              let e = yield O(b);
              e || (e = []);
              const t = e.findIndex((e) => e.downloadId === n.downloadId);
              (-1 === t
                ? e.unshift(n)
                : (e[t] = Object.assign(Object.assign({}, e[t]), n)),
                yield P(b, e));
            });
          })(r);
        });
      }
      function k(n) {
        return e(this, void 0, void 0, function* () {
          let t = u.processVideoInWeb;
          n.downloadMethod == u.processVideoInWeb && (t = u.processVideo);
          let o = {
            downloadId: n.downloadId,
            courseId: n.courseId,
            lectureId: n.lectureId,
            videoName: n.videoName,
            downloadTime: n.downloadTime,
            secretKey: n.secretKey,
            downloadMethod: t,
            webUrl: n.webUrl,
            status: d.downloadWating,
            msg: "Waiting for download...",
          };
          (yield (function (n) {
            return e(this, void 0, void 0, function* () {
              let e = yield O(v);
              e || (e = []);
              const t = e.findIndex((e) => e.downloadId === n.downloadId);
              (-1 !== t
                ? (e[t] = Object.assign(Object.assign({}, e[t]), n))
                : e.unshift(n),
                yield P(v, e));
            });
          })(o),
            yield (function (n) {
              return e(this, void 0, void 0, function* () {
                let e = yield O(b);
                e || (e = []);
                const t = e.findIndex((e) => e.downloadId === n);
                -1 !== t && (e.splice(t, 1), yield P(b, e));
              });
            })(o.downloadId));
        });
      }
      function M(n) {
        return e(this, void 0, void 0, function* () {
          if ("downloading" === n) return (yield O(y)) || [];
          if ("downloaded" === n) return (yield O(b)) || [];
          if ("download" === n) return (yield O(v)) || [];
          throw new Error(
            'Invalid type specified. Use "downloading" or "downloaded".',
          );
        });
      }
      function T(e) {
        // Allow all domains - universal video downloader
        return true;
      }
      function B(e, n, t) {
        const o = (n - 1) * t,
          r = o + t;
        return e.slice(o, r);
      }
      function $(e) {
        const { startOfDay: n, endOfDay: t } = (function () {
          const e = new Date();
          return {
            startOfDay: new Date(
              e.getFullYear(),
              e.getMonth(),
              e.getDate(),
              0,
              0,
              0,
              0,
            ),
            endOfDay: new Date(
              e.getFullYear(),
              e.getMonth(),
              e.getDate(),
              23,
              59,
              59,
              999,
            ),
          };
        })();
        return e.filter((e) => {
          const o = new Date(e.downloadTime);
          return o >= n && o <= t;
        });
      }
      function L() {
        return e(this, void 0, void 0, function* () {
          const e = $(yield M("downloaded")),
            { successList: n, otherList: t } = (function (e) {
              const n = [],
                t = [];
              return (
                e.forEach((e) => {
                  e.status === d.downloadSuccess ? n.push(e) : t.push(e);
                }),
                { successList: n, otherList: t }
              );
            })(e);
          return { successList: n, otherList: t };
        });
      }
      var N, U, j;
      (!(function (e) {
        ((e.download = "download"),
          (e.progress = "progress"),
          (e.end = "end"),
          (e.cancle = "cancle"),
          (e.merge = "merge"),
          (e.error = "error"));
      })(N || (N = {})),
        (function (e) {
          ((e.downloadingToDownloaded = "downloadingToDownloaded"),
            (e.downloadToDownloaded = "downloadToDownloaded"));
        })(U || (U = {})));
      class F {
        constructor(e) {
          const n = {
            container: document.body,
            currentPage: 1,
            totalItems: 0,
            itemsPerPage: 10,
            visiblePages: 5,
            onPageChange: () => {},
            css: {
              containerClass: "pagination",
              itemClass: "page-item",
              linkClass: "page-link",
              activeClass: "active",
              disabledClass: "disabled",
              ellipsisClass: "ellipsis",
            },
          };
          ((this.config = Object.assign(Object.assign({}, n), e)),
            (this.totalPages = Math.ceil(
              this.config.totalItems / this.config.itemsPerPage,
            )),
            (this.pagination = document.createElement("ul")),
            this.init());
        }
        init() {
          (this.createPagination(), this.bindEvents());
        }
        createPagination() {
          ((this.pagination.className = this.config.css.containerClass),
            this.renderPages(),
            this.config.container.appendChild(this.pagination));
        }
        renderPages() {
          ((this.pagination.innerHTML = ""),
            this.addPrevButton(),
            this.addPageButton(1),
            this.totalPages > this.config.visiblePages &&
              this.config.currentPage > 3 &&
              this.addEllipsis());
          const { startPage: e, endPage: n } = this.calculatePageRange();
          for (let t = e; t <= n; t++)
            t > 1 && t < this.totalPages && this.addPageButton(t);
          (this.totalPages > this.config.visiblePages &&
            this.config.currentPage < this.totalPages - 2 &&
            this.addEllipsis(),
            this.totalPages > 1 && this.addPageButton(this.totalPages),
            this.addNextButton());
        }
        calculatePageRange() {
          let e = Math.max(2, this.config.currentPage - 1),
            n = Math.min(this.totalPages - 1, this.config.currentPage + 1);
          return (
            this.config.currentPage <= 3
              ? ((e = 2), (n = Math.min(4, this.totalPages - 1)))
              : this.config.currentPage >= this.totalPages - 2 &&
                ((e = Math.max(this.totalPages - 3, 2)),
                (n = this.totalPages - 1)),
            { startPage: e, endPage: n }
          );
        }
        addPrevButton() {
          const e = document.createElement("li");
          ((e.className = `${this.config.css.itemClass} ${1 === this.config.currentPage ? this.config.css.disabledClass : ""}`),
            (e.innerHTML = `<a class="${this.config.css.linkClass}" href="#" data-page="prev">«</a>`),
            this.pagination.appendChild(e));
        }
        addNextButton() {
          const e = document.createElement("li");
          ((e.className = `${this.config.css.itemClass} ${this.config.currentPage === this.totalPages ? this.config.css.disabledClass : ""}`),
            (e.innerHTML = `<a class="${this.config.css.linkClass}" href="#" data-page="next">»</a>`),
            this.pagination.appendChild(e));
        }
        addPageButton(e) {
          const n = document.createElement("li");
          ((n.className = `${this.config.css.itemClass} ${e === this.config.currentPage ? this.config.css.activeClass : ""}`),
            (n.innerHTML = `<a class="${this.config.css.linkClass}" href="#" data-page="${e}">${e}</a>`),
            this.pagination.appendChild(n));
        }
        addEllipsis() {
          const e = document.createElement("li");
          ((e.className = `${this.config.css.itemClass} ${this.config.css.ellipsisClass}`),
            (e.innerHTML = "<span>...</span>"),
            this.pagination.appendChild(e));
        }
        bindEvents() {
          this.pagination.addEventListener("click", (e) => {
            e.preventDefault();
            const n = e.target.closest("a");
            if (!n) return;
            const t = n.dataset.page;
            this.handlePageChange(t);
          });
        }
        handlePageChange(e) {
          let n = this.config.currentPage;
          ("prev" === e && this.config.currentPage > 1
            ? n--
            : "next" === e && this.config.currentPage < this.totalPages
              ? n++
              : e && !isNaN(Number(e)) && (n = parseInt(e, 10)),
            n !== this.config.currentPage &&
              n > 0 &&
              n <= this.totalPages &&
              ((this.config.currentPage = n),
              this.renderPages(),
              this.config.onPageChange(this.config.currentPage)));
        }
        update(e) {
          ((this.config = Object.assign(Object.assign({}, this.config), e)),
            (this.totalPages = Math.ceil(
              this.config.totalItems / this.config.itemsPerPage,
            )),
            this.renderPages());
        }
        destroy() {
          this.pagination.remove();
        }
      }
      let W = (j = class {
        constructor() {
          ((this.downloadPagination = null),
            (this.downloadedPagination = null),
            j.rendQuota(),
            j.rendDownloadingInfo(),
            j.rendDownloadInfo(),
            j.rendDownloadedInfo(),
            p().runtime.onConnect.addListener((e) => {
              "sidepanelsNotification" === e.name &&
                (e.hasSidepanelsNotificationListener ||
                  ((e.hasSidepanelsNotificationListener = !0),
                  e.onMessage.addListener(j.onMessage)));
            }),
            (document.getElementById("clearDownload").onclick = () => {
              (function (n) {
                return e(this, void 0, void 0, function* () {
                  if ("downloading" === n) yield P(y, []);
                  else if ("downloaded" === n) yield P(b, []);
                  else {
                    if ("download" !== n)
                      throw new Error(
                        'Invalid type specified. Use "downloading" or "downloaded" or "download".',
                      );
                    yield P(v, []);
                  }
                });
              })("download").then(() => {
                j.rendDownloadInfo();
              });
            }),
            (document.getElementById("getMoreQuota").onclick = () => {
              j.jumpPay();
            }));
        }
        static onMessage(n) {
          return e(this, void 0, void 0, function* () {
            n.action === c.refresh &&
              (n.refreshType === g.downloading
                ? j.rendDownloadingInfo()
                : n.refreshType === g.downloaded
                  ? (j.rendQuota(), j.rendDownloadedInfo())
                  : n.refreshType === g.download
                    ? (j.rendQuota(), j.rendDownloadInfo())
                    : n.refreshType === g.all
                      ? (j.rendQuota(),
                        j.rendDownloadingInfo(),
                        j.rendDownloadedInfo(),
                        j.rendDownloadInfo())
                      : n.refreshType === g.quota && j.rendQuota());
          });
        }
        static rendQuota() {
          return e(this, void 0, void 0, function* () {
            const {
              downloadLimit: n,
              noPerDayMaxDownloadCount: t,
              noPerDayDownloadedCount: r,
            } = yield (function () {
              return e(this, void 0, void 0, function* () {
                let e = parseInt("2"),
                  n = 0,
                  t = 0;
                try {
                  let e = {
                    videoUrlList: [],
                    videoNameList: [],
                    type: o.getMemberInfo,
                  };
                  yield p().runtime.sendMessage(e);
                } catch (e) {
                } finally {
                  let o = yield I();
                  e = null == o.maxDownloadCount ? 0 : o.maxDownloadCount;
                  let r = o.noPerDayMemberList;
                  if (null != r && r.length > 0)
                    for (let e = 0; e < r.length; e++) {
                      let o = yield O(r[e].id + ""),
                        i = null == o ? 0 : o,
                        a =
                          null == r[e].downloadedCount
                            ? 0
                            : r[e].downloadedCount;
                      (a > i && ((i = a), yield P(r[e].id + "", i)),
                        (t += i),
                        (n += r[e].downloadCount));
                    }
                }
                return {
                  downloadLimit: e,
                  noPerDayMaxDownloadCount: n,
                  noPerDayDownloadedCount: t,
                };
              });
            })();
            let i = (yield I()).downloadedCount;
            ((i = i < 0 ? 0 : i), i > n && (i = n));
            let a = (yield O(y)) || [],
              s = (yield O(v)) || [],
              d = i,
              l = r;
            const c = a.length + s.length;
            let g = n - i;
            g < 0 && (g = 0);
            let u = g - c;
            // Show unlimited quota - local activation
            (document.getElementById("usedQuota").textContent = "0"),
            (document.getElementById("todayQuota").textContent = "∞"),
            (document.getElementById("fixedQuotaDiv").style.display = "none");
          });
        }
        static rendConcurrentDownloadsOption() {
          return e(this, void 0, void 0, function* () {
            try {
              const n = yield (function () {
                  return e(this, void 0, void 0, function* () {
                    let e = yield O("concurrentDownloadsOption");
                    return (e || (e = "1,2,3"), e.split(","));
                  });
                })(),
                t = yield (function () {
                  return e(this, void 0, void 0, function* () {
                    let e = yield O("maxConcurrentDownloads");
                    return (e || (e = parseInt("1")), e);
                  });
                })(),
                o = document.getElementById("downloadCountConfig"),
                r = document.getElementById("downloadCountConfig-header"),
                i = document.getElementById("downloadCountConfig-list");
              r.textContent = t.toString();
              let a = "";
              (n.forEach((e) => {
                a +=
                  '<div class="dropdown-item" data-value="' +
                  parseInt(e) +
                  '">' +
                  parseInt(e) +
                  "</div>";
              }),
                (i.innerHTML = a));
              const s = document.querySelectorAll(".dropdown-item");
              (r.addEventListener("click", () => {
                o.classList.toggle("open");
              }),
                s.forEach((n) => {
                  (n.textContent == t.toString() && n.classList.add("selected"),
                    n.addEventListener("click", (n) =>
                      e(this, void 0, void 0, function* () {
                        (s.forEach((e) => e.classList.remove("selected")),
                          n.target.classList.add("selected"),
                          (r.textContent = n.target.textContent),
                          o.classList.remove("open"),
                          yield S(parseInt(n.target.textContent)));
                      }),
                    ));
                }),
                document.addEventListener("click", (e) => {
                  const n = e.target;
                  o.contains(n) || o.classList.remove("open");
                }));
            } catch (e) {}
          });
        }
        static rendDownloadingInfo() {
          return e(this, void 0, void 0, function* () {
            let e = yield M("downloading"),
              n = "";
            for (let t = 0; t < e.length; t++) {
              let o = e[t],
                r = "assets/imgs/eglass_close.png",
                i = "cancel download";
              o.status == d.downloadStuck &&
                ((r = "assets/imgs/rocket_download.png"),
                (i = "change to power download"));
              let a =
                  '\n                <div class="downloading-cancle">\n                    <img id="downloadingBtn_' +
                  o.downloadId +
                  '" src="' +
                  r +
                  '" title="' +
                  i +
                  '"/>\n                </div>\n            ',
                s = "";
              (o.downloadMethod == u.processVideo &&
                (s = ' title="' + o.videoName + '"'),
                (n +=
                  '\n                <div class="progress-container-s" id="' +
                  o.downloadId +
                  '">\n                    <div class="downloaded-item-content">\n                        <div class="progress-bar-wrapper">\n                            <div class="progress-bar">\n                                <div class="progress-label" ' +
                  s +
                  ">" +
                  o.videoName +
                  '</div>\n                                <div class="progress-percentage">' +
                  o.percent +
                  '%</div>\n                                <div class="blue" style="width:' +
                  o.percent +
                  '%"></div>\n                            </div>\n                        </div>\n                        ' +
                  a +
                  "\n                    </div>\n                </div>\n            "));
            }
            let t = document.getElementById("downloadingDiv");
            if ("" != n) {
              t.innerHTML = n;
              const o = e.filter((e) => e.status != d.downloadStuck),
                r = e.filter((e) => e.status === d.downloadStuck);
              requestAnimationFrame(() => {
                (o.forEach((e) => {
                  const n = document.getElementById(
                    "downloadingBtn_" + e.downloadId,
                  );
                  n &&
                    (n.onclick = () => {
                      j.cancleDownloading(e);
                    });
                }),
                  r.forEach((e) => {
                    const n = document.getElementById(
                      "downloadingBtn_" + e.downloadId,
                    );
                    n &&
                      (n.onclick = () => {
                        j.startStuckDownloading(e);
                      });
                  }));
              });
            } else
              t.innerHTML =
                '<span class="nodata">No data being downloading</span>';
          });
        }
        static rendDownloadInfo() {
          return e(this, void 0, void 0, function* () {
            let e = yield M("download"),
              n = document.getElementById("downloadDiv");
            if (e.length > 0) {
              const n = 4;
              let t = 1;
              if (
                ((document.getElementById("download-container").style.display =
                  "block"),
                (document.getElementById("downloadRecords").textContent =
                  e.length + ""),
                V.downloadPagination)
              ) {
                let o = Math.ceil(e.length / n);
                t =
                  V.downloadPagination.config.currentPage > o
                    ? o
                    : V.downloadPagination.config.currentPage;
              }
              let o = B(e, t, n);
              j.setDownloadHtml(o);
              const r = {
                container: document.getElementById("download-pagination"),
                currentPage: t,
                totalItems: e.length,
                itemsPerPage: n,
                visiblePages: 5,
                onPageChange: (t) => {
                  let o = B(e, t, n);
                  j.setDownloadHtml(o);
                },
              };
              V.downloadPagination
                ? V.downloadPagination.update(r)
                : (V.downloadPagination = new F(r));
            } else
              ((n.innerHTML =
                '<span class="nodata">No download in queue data</span>'),
                (document.getElementById("download-container").style.display =
                  "none"));
          });
        }
        static setDownloadHtml(n) {
          return e(this, void 0, void 0, function* () {
            let e = "";
            for (let t = 0; t < n.length; t++) {
              let o = n[t];
              e +=
                '\n                <div class="downloaded-item">\n                    <div class="downloaded-item-content">\n                        <div class="downloaded-item-content-left">\n                            ' +
                ('<div class="download-label" title="' +
                  o.videoName +
                  '">' +
                  o.videoName +
                  "</div>") +
                "\n                        </div>\n                        " +
                ('\n                <div class="downloading-cancle">\n                    <img id="downloadBtn_' +
                  o.downloadId +
                  '" src="assets/imgs/eglass_close.png"/>\n                </div>\n            ') +
                "\n                    </div>\n                </div>\n            ";
            }
            ((document.getElementById("downloadDiv").innerHTML = e),
              requestAnimationFrame(() => {
                n.forEach((e) => {
                  const n = document.getElementById(
                    "downloadBtn_" + e.downloadId,
                  );
                  n &&
                    (n.onclick = () => {
                      j.cancleDownload(e);
                    });
                });
              }));
          });
        }
        static rendDownloadedInfo() {
          return e(this, void 0, void 0, function* () {
            let e = document.getElementById("downloadedDiv"),
              n = (yield L()).successList;
            if (n.length > 0) {
              const e = 3;
              let t = 1;
              if (
                ((document.getElementById(
                  "downloaded-container",
                ).style.display = "block"),
                (document.getElementById("downloadedRecords").textContent =
                  n.length + ""),
                V.downloadedPagination)
              ) {
                let o = Math.ceil(n.length / e);
                t =
                  V.downloadedPagination.config.currentPage > o
                    ? o
                    : V.downloadedPagination.config.currentPage;
              }
              let o = B(n, t, e);
              j.setDownloadedHtml(o);
              const r = {
                container: document.getElementById("downloaded-pagination"),
                currentPage: t,
                totalItems: n.length,
                itemsPerPage: e,
                visiblePages: 5,
                onPageChange: (t) => {
                  let o = B(n, t, e);
                  j.setDownloadedHtml(o);
                },
              };
              V.downloadedPagination
                ? V.downloadedPagination.update(r)
                : (V.downloadedPagination = new F(r));
            } else
              ((e.innerHTML = '<span class="nodata">No downloaded data</span>'),
                (document.getElementById("downloaded-container").style.display =
                  "none"));
          });
        }
        static setDownloadedHtml(n) {
          return e(this, void 0, void 0, function* () {
            let e = "";
            for (let t = 0; t < n.length; t++) {
              let o = n[t];
              e +=
                '\n                <div class="downloaded-item">\n                    <div class="downloaded-item-content">\n                        <div class="downloaded-item-content-left">\n                            ' +
                ('<div class="downloaded-label" title="' +
                  o.videoName +
                  '">' +
                  o.videoName +
                  "</div>") +
                '    \n                            <div class="secondp">' +
                j.formatDate(o.downloadTime) +
                "</div>\n                        </div>\n                    </div>\n                </div>\n            ";
            }
            document.getElementById("downloadedDiv").innerHTML = e;
          });
        }
        static cancleDownloading(n) {
          return e(this, void 0, void 0, function* () {
            let e = u.processVideo;
            n.downloadMethod == u.processVideo && (e = u.processVideoInWeb);
            let t = {
                downloadId: n.downloadId,
                courseId: n.courseId,
                mediaUrl: n.mediaUrl,
                filenameOutput: n.videoName,
                lectureId: n.lectureId,
                secretKey: n.secretKey,
                downloadMethod: e,
                userId: "",
                version: "",
                downloadSegmentsCount: 0,
                webUrl: n.webUrl,
                downloadTime: n.downloadTime,
              },
              o = { action: N.cancle, workerDownloadMessage: t },
              r = p().runtime.connect({ name: "cancleDownloading" });
            ((r.hasCancleDownloadingListener = !0),
              r.postMessage(o),
              r.disconnect());
          });
        }
        static startStuckDownloading(n) {
          return e(this, void 0, void 0, function* () {
            let e = p().runtime.connect({ name: "startStuck" });
            (e.postMessage(n), e.disconnect());
          });
        }
        static cancleDownload(n) {
          return e(this, void 0, void 0, function* () {
            let e = {
              downloadId: n.downloadId,
              courseId: n.courseId,
              mediaUrl: n.mediaUrl,
              filenameOutput: n.videoName,
              lectureId: n.lectureId,
              secretKey: n.secretKey,
              downloadMethod: u.processVideo,
              userId: "",
              version: "",
              downloadSegmentsCount: 0,
              webUrl: n.webUrl,
              downloadTime: n.downloadTime,
            };
            yield E(e, d.downloadCancle, "User has cancelled");
          });
        }
        static retryDownloaded(n) {
          return e(this, void 0, void 0, function* () {
            let { isNeedLogin: e, serverIsError: t } = yield C(0);
            if (e) {
              let e = { type: m.tip };
              t && (e.type = m.serverError);
              let n = p().runtime.connect({ name: "retryMessage" });
              (n.postMessage(e), n.disconnect());
            } else ((n.downloadMethod = u.processVideoInWeb), yield k(n));
          });
        }
        static jumpPay() {
          return e(this, void 0, void 0, function* () {
            const e =
              "https://www.bestaddons.store/ytbdserver/downloader/webtransfer/ude/pay-guide?userId=" +
              (yield I()).userId +
              "&extId=a5376339-a50f-f096-248c-0e0cdde4f9af&version=" +
              p().runtime.getManifest().version;
            window.open(e + "", "_blank");
          });
        }
        static formatDate(e) {
          let n = "";
          try {
            const t = new Date(e);
            let o = t.getHours(),
              r = t.getMinutes();
            const i = o >= 12 ? "PM" : "AM";
            o = o % 12 || 12;
            let a = r < 10 ? "0" + r : r;
            const s = t.getMonth() + 1,
              d = t.getDate();
            n = `${o}:${a} ${i} ${s}/${d}/${t.getFullYear()}`;
          } catch (e) {
            console.log(e);
          }
          return n;
        }
      });
      ((W.openSidebarConfigName = "openSidebarConfig"),
        (W = j =
          (function (e, n, t, o) {
            var r,
              i = arguments.length,
              a =
                i < 3
                  ? n
                  : null === o
                    ? (o = Object.getOwnPropertyDescriptor(n, t))
                    : o;
            if (
              "object" == typeof Reflect &&
              "function" == typeof Reflect.decorate
            )
              a = Reflect.decorate(e, n, t, o);
            else
              for (var s = e.length - 1; s >= 0; s--)
                (r = e[s]) &&
                  (a = (i < 3 ? r(a) : i > 3 ? r(n, t, a) : r(n, t)) || a);
            return (i > 3 && a && Object.defineProperty(n, t, a), a);
          })(
            [
              function (e) {
                return new Proxy(e, {
                  construct: (e, n, t) =>
                    e.prototype !== t.prototype
                      ? Reflect.construct(e, n, t)
                      : (e.SINGLETON_INSTANCE ||
                          (e.SINGLETON_INSTANCE = Reflect.construct(e, n, t)),
                        e.SINGLETON_INSTANCE),
                });
              },
            ],
            W,
          )));
      const V = new W();
    })());
})();
